#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析您的HRCLDAS文件的完整信息
"""

import os
import sys
from analyze_hrcldas_data import HRCLDASAnalyzer

def analyze_your_file():
    """分析您的具体文件"""
    
    # 您的文件名
    filename = "Z_NAFP_C_BABJ_20240101000535_P_HRCLDAS_RT_BENN_0P01_HOR-QAIR-2024010100.GRB2"
    
    # 如果您有实际文件，请修改为完整路径
    # filepath = r"C:\path\to\your\file\Z_NAFP_C_BABJ_20240101000535_P_HRCLDAS_RT_BENN_0P01_HOR-QAIR-2024010100.GRB2"
    filepath = filename  # 当前使用文件名作为示例
    
    print("🔍 使用现有HRCLDASAnalyzer分析您的文件")
    print("="*60)
    
    # 创建分析器实例
    analyzer = HRCLDASAnalyzer()
    
    # 使用现有方法分析文件
    result = analyzer.analyze_grib_file(filepath)
    
    print("\n📝 总结:")
    print("1. 现有代码可以直接处理您的文件")
    print("2. 文件名格式完全匹配现有正则表达式")
    print("3. 如果您有实际文件，只需提供正确的文件路径")
    print("4. 所有现有功能都可以正常使用")
    
    return result

if __name__ == "__main__":
    analyze_your_file()
