#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新HRCLDAS文件的信息提取
"""

import os
import re
import pygrib
import xarray as xr
import numpy as np
from datetime import datetime
from pathlib import Path

class HRCLDASFileAnalyzer:
    """HRCLDAS文件分析器"""
    
    def __init__(self):
        self.file_patterns = {
            'hrcldas': r'Z_NAFP_C_BABJ_(\d{14})_P_HRCLDAS_RT_BENN_0P01_HOR-(\w+)-(\d{10})\.GRB2?'
        }
        
        # HRCLDAS变量映射
        self.variable_info = {
            'QAIR': {'name': '比湿', 'units': 'kg/kg', 'description': '2米比湿'},
            'TMP': {'name': '温度', 'units': 'K', 'description': '2米温度'},
            'UGRD': {'name': 'U风分量', 'units': 'm/s', 'description': '10米U风分量'},
            'VGRD': {'name': 'V风分量', 'units': 'm/s', 'description': '10米V风分量'},
            'PRES': {'name': '气压', 'units': 'Pa', 'description': '地面气压'},
            'APCP': {'name': '降水', 'units': 'kg/m^2', 'description': '累积降水'},
            'DSWRF': {'name': '短波辐射', 'units': 'W/m^2', 'description': '向下短波辐射通量'},
            'DLWRF': {'name': '长波辐射', 'units': 'W/m^2', 'description': '向下长波辐射通量'}
        }
    
    def parse_filename(self, filename):
        """解析HRCLDAS文件名"""
        filename = Path(filename).name
        
        pattern = self.file_patterns['hrcldas']
        match = re.match(pattern, filename)
        
        if match:
            creation_time = match.group(1)  # 文件创建时间
            variable = match.group(2)       # 变量名
            data_time = match.group(3)      # 数据时间
            
            try:
                creation_dt = datetime.strptime(creation_time, "%Y%m%d%H%M%S")
                data_dt = datetime.strptime(data_time, "%Y%m%d%H")
                
                # 获取变量详细信息
                var_info = self.variable_info.get(variable, {
                    'name': variable, 
                    'units': 'unknown', 
                    'description': f'未知变量: {variable}'
                })
                
                return {
                    'valid': True,
                    'file_type': 'HRCLDAS',
                    'creation_time': creation_dt,
                    'data_time': data_dt,
                    'variable_code': variable,
                    'variable_name': var_info['name'],
                    'variable_units': var_info['units'],
                    'variable_description': var_info['description'],
                    'resolution': '0.01°',
                    'domain': 'BENN',
                    'format': 'horizontal',
                    'original_filename': filename
                }
            except ValueError as e:
                return {'valid': False, 'error': f'时间解析错误: {e}'}
        
        return {'valid': False, 'error': '文件名格式不匹配'}
    
    def analyze_file_info(self, filename):
        """分析文件信息并输出详细报告"""
        print(f"{'='*80}")
        print(f"HRCLDAS文件信息分析")
        print(f"{'='*80}")
        print(f"文件名: {filename}")
        print(f"{'='*80}")
        
        # 解析文件名
        file_info = self.parse_filename(filename)
        
        if not file_info['valid']:
            print(f"❌ 解析失败: {file_info['error']}")
            return None
        
        # 输出基本信息
        print(f"✅ 文件名解析成功!")
        print(f"\n📋 基本信息:")
        print(f"  文件类型      : {file_info['file_type']}")
        print(f"  空间分辨率    : {file_info['resolution']}")
        print(f"  覆盖区域      : {file_info['domain']}")
        print(f"  数据格式      : {file_info['format']}")
        
        print(f"\n⏰ 时间信息:")
        print(f"  文件创建时间  : {file_info['creation_time'].strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"  数据时间      : {file_info['data_time'].strftime('%Y-%m-%d %H:%M:%S')}")
        
        print(f"\n🌡️  变量信息:")
        print(f"  变量代码      : {file_info['variable_code']}")
        print(f"  变量名称      : {file_info['variable_name']}")
        print(f"  变量单位      : {file_info['variable_units']}")
        print(f"  变量描述      : {file_info['variable_description']}")
        
        return file_info
    
    def analyze_grib_content(self, filepath):
        """分析GRIB文件内容（如果文件存在）"""
        if not os.path.exists(filepath):
            print(f"\n⚠️  文件不存在: {filepath}")
            print("   以下是如果文件存在时的分析方法:")
            return self._show_analysis_methods()
        
        print(f"\n📊 GRIB文件内容分析:")
        print(f"{'='*50}")
        
        try:
            # 使用pygrib分析
            self._analyze_with_pygrib(filepath)
            
            # 使用xarray分析
            self._analyze_with_xarray(filepath)
            
        except Exception as e:
            print(f"❌ 分析失败: {str(e)}")
    
    def _analyze_with_pygrib(self, filepath):
        """使用pygrib分析GRIB文件"""
        print(f"\n🔍 PYGRIB分析结果:")
        try:
            with pygrib.open(filepath) as grbs:
                message_count = 0
                for grb in grbs:
                    message_count += 1
                    print(f"  消息 {message_count}:")
                    print(f"    变量名     : {getattr(grb, 'name', 'N/A')}")
                    print(f"    短名称     : {getattr(grb, 'shortName', 'N/A')}")
                    print(f"    单位       : {getattr(grb, 'units', 'N/A')}")
                    print(f"    层次类型   : {getattr(grb, 'typeOfLevel', 'N/A')}")
                    print(f"    层次值     : {getattr(grb, 'level', 'N/A')}")
                    print(f"    数据形状   : {grb.values.shape}")
                    print(f"    数据范围   : {grb.values.min():.4f} ~ {grb.values.max():.4f}")
                    
                    # 获取坐标信息
                    lats, lons = grb.latlons()
                    print(f"    纬度范围   : {lats.min():.4f}° ~ {lats.max():.4f}°")
                    print(f"    经度范围   : {lons.min():.4f}° ~ {lons.max():.4f}°")
                    print()
                
                print(f"  总消息数: {message_count}")
                
        except Exception as e:
            print(f"    PYGRIB分析失败: {str(e)}")
    
    def _analyze_with_xarray(self, filepath):
        """使用xarray分析GRIB文件"""
        print(f"\n📈 XARRAY分析结果:")
        try:
            ds = xr.open_dataset(filepath, engine='cfgrib')
            
            print(f"  数据集维度: {dict(ds.dims)}")
            print(f"  数据变量:")
            for var_name, var in ds.data_vars.items():
                print(f"    {var_name}: {var.dims} {var.shape}")
                if hasattr(var, 'units'):
                    print(f"      单位: {var.units}")
                if hasattr(var, 'long_name'):
                    print(f"      描述: {var.long_name}")
            
            print(f"  坐标变量:")
            for coord_name, coord in ds.coords.items():
                print(f"    {coord_name}: {coord.dims} {coord.shape}")
                if coord.size <= 10:
                    print(f"      值: {coord.values}")
                else:
                    print(f"      范围: {coord.min().values} ~ {coord.max().values}")
            
            ds.close()
            
        except Exception as e:
            print(f"    XARRAY分析失败: {str(e)}")
    
    def _show_analysis_methods(self):
        """展示分析方法"""
        print(f"\n📚 GRIB文件分析方法:")
        print(f"  1. 使用pygrib库:")
        print(f"     import pygrib")
        print(f"     with pygrib.open('文件路径') as grbs:")
        print(f"         for grb in grbs:")
        print(f"             print(grb.name, grb.values.shape)")
        print(f"")
        print(f"  2. 使用xarray库:")
        print(f"     import xarray as xr")
        print(f"     ds = xr.open_dataset('文件路径', engine='cfgrib')")
        print(f"     print(ds)")

def main():
    """主函数"""
    # 您提到的新文件名
    new_filename = "Z_NAFP_C_BABJ_20240101000535_P_HRCLDAS_RT_BENN_0P01_HOR-QAIR-2024010100.GRB2"
    
    # 创建分析器
    analyzer = HRCLDASFileAnalyzer()
    
    # 分析文件信息
    file_info = analyzer.analyze_file_info(new_filename)
    
    if file_info:
        # 如果有实际文件路径，可以分析文件内容
        # analyzer.analyze_grib_content(new_filename)
        
        print(f"\n💡 使用建议:")
        print(f"  1. 现有代码完全兼容此文件格式")
        print(f"  2. 无需修改解析逻辑")
        print(f"  3. 如果要处理实际文件，请提供完整文件路径")
        print(f"  4. 可以使用现有的analyze_grib_file()方法")

if __name__ == "__main__":
    main()
